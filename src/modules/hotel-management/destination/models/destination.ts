import { model } from "@camped-ai/framework/utils";
import { DestinationImage } from "./destination-image";
import { DestinationFaq } from "./destination-faq";

export const Destination = model.define("destination", {
  id: model.id().primaryKey(),
  name: model.text(),
  handle: model.text(),
  description: model.text().nullable(),
  is_active: model.boolean().default(true),
  country: model.text(),
  location: model.text().nullable(),
  tags: model.json().nullable(),
  is_featured: model.boolean().default(false),
  ai_content: model.text().nullable(),
  metadata: model.json().nullable(),
  images: model.hasMany(() => DestinationImage, {
      foreignKey: "destination_id",
      localKey: "id",
    }),
  faqs: model.hasMany(() => DestinationFaq, {
      foreignKey: "destination_id",
      localKey: "id",
    }),
});
