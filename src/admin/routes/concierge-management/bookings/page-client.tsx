import { Calendar, Copy } from "lucide-react";
import { Container, Heading, Text, Badge, Toaster, toast } from "@camped-ai/ui";

import { useMemo, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  useReactTable,
  getCoreRowModel,
  createColumnHelper,
  ColumnDef,
} from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import type { Filter } from "../../../../components/table/data-table";
import { DataTableQuery } from "../../../../components/table/data-table/data-table-query";
import { DataTableRoot } from "../../../../components/table/data-table/data-table-root";
import { type BookingScreenData, type BookingScreenFilters } from "./loader";
import { sdk } from "../../../lib/sdk";
import { ViewMode } from "../../../components/concierge/view-toggle";
import { KanbanView } from "../../../components/concierge/kanban-view";
import { CalendarView } from "../../../components/concierge/calendar-view";
import {
  BookingFilterState,
} from "../../../components/concierge/booking-filter-controls";

// Status badge colors
const statusColors = {
  not_started: "grey",
  in_progress: "blue",
  waiting_customer: "orange",
  ready_to_finalize: "purple",
  completed: "green",
  under_review: "orange",
  canceled: "red",
  pending: "orange",
  confirmed: "green",
  checked_in: "blue",
  checked_out: "purple",
  no_show: "grey",
} as const;

// Function to fetch hotels for the dropdown
const fetchHotels = async () => {
  try {
    const response = await sdk.client.fetch(
      "/admin/hotel-management/hotels/list-basic"
    );
    return (response as any).hotels || [];
  } catch (error) {
    console.error("Error fetching hotels:", error);
    return [];
  }
};

// Function to fetch users for the assigned_to dropdown - using same logic as detail page
const fetchUsers = async () => {
  try {
    const response = await fetch(
      "/admin/users?limit=50&fields=id,email,first_name,last_name"
    );
    if (!response.ok) {
      throw new Error("Failed to fetch users");
    }
    const data = await response.json();
    return data.users || [];
  } catch (error) {
    console.error("Error fetching users:", error);
    return [];
  }
};

// Helper function to get user display name - same logic as detail page
const getUserDisplayName = (user: any) => {
  return user.first_name && user.last_name
    ? `${user.first_name} ${user.last_name}`
    : user.email;
};

const BookingsPageClient: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { hasPermission } = useRbac();
  const [filterState, setFilterState] = useState<BookingFilterState>({
    search: "",
    hotel_id: [],
    assigned_to: [],
    status: [],
  });

  // Get URL search params (using same pattern as supplier page)
  const searchParams = new URLSearchParams(location.search);

  // Get current view from searchParams
  const currentView = (searchParams.get("view") as ViewMode) || "list";
  const hotelId = searchParams.get("hotel_id");

  // Get current page and page size from URL params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "20");


  // Build filters for the loader
  const filters: BookingScreenFilters = useMemo(() => {
    const baseFilters: BookingScreenFilters = {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      sort_by: searchParams.get("order")?.replace("-", "") || "created_at",
      sort_order: searchParams.get("order")?.startsWith("-") ? "desc" : "asc",
    };

    // Add search query
    const searchQuery = searchParams.get("q");
    if (searchQuery) {
      baseFilters.q = searchQuery;
    }

    // Add filters from URL params
    const statusFilter = searchParams.get("status");
    if (statusFilter) {
      baseFilters.status = statusFilter;
    }

    const assignedToFilter = searchParams.get("assigned_to");
    if (assignedToFilter) {
      baseFilters.assigned_to = assignedToFilter;
    }

    const customerNameFilter = searchParams.get("customer_name");
    if (customerNameFilter) {
      baseFilters.customer_name = customerNameFilter;
    }

    const hotelFilter = searchParams.get("hotel_id") || hotelId;
    if (hotelFilter) {
      baseFilters.hotel_id = hotelFilter;
    }

    // Add date range filters - handle direct date parameters with validation
    const checkInDateGte = searchParams.get("check_in_date_gte");
    const checkInDateLte = searchParams.get("check_in_date_lte");
    const checkOutDateGte = searchParams.get("check_out_date_gte");
    const checkOutDateLte = searchParams.get("check_out_date_lte");

    // Validate and add check-in date filters
    if (checkInDateGte) {
      try {
        const date = new Date(checkInDateGte);
        if (!isNaN(date.getTime())) {
          baseFilters.check_in_date_gte = checkInDateGte;
        }
      } catch (error) {
        console.warn("Invalid check_in_date_gte:", checkInDateGte, error);
      }
    }

    if (checkInDateLte) {
      try {
        const date = new Date(checkInDateLte);
        if (!isNaN(date.getTime())) {
          baseFilters.check_in_date_lte = checkInDateLte;
        }
      } catch (error) {
        console.warn("Invalid check_in_date_lte:", checkInDateLte, error);
      }
    }

    // Validate and add check-out date filters
    if (checkOutDateGte) {
      try {
        const date = new Date(checkOutDateGte);
        if (!isNaN(date.getTime())) {
          baseFilters.check_out_date_gte = checkOutDateGte;
        }
      } catch (error) {
        console.warn("Invalid check_out_date_gte:", checkOutDateGte, error);
      }
    }

    if (checkOutDateLte) {
      try {
        const date = new Date(checkOutDateLte);
        if (!isNaN(date.getTime())) {
          baseFilters.check_out_date_lte = checkOutDateLte;
        }
      } catch (error) {
        console.warn("Invalid check_out_date_lte:", checkOutDateLte, error);
      }
    }

    // Legacy payment status filter
    const paymentStatusFilter = searchParams.get("payment_status");
    if (paymentStatusFilter) {
      baseFilters.payment_status = paymentStatusFilter;
    }

    return baseFilters;
  }, [searchParams, currentPage, pageSize, hotelId]);

  // Direct API call function with enhanced error handling
  const fetchBookings = async (filters: BookingScreenFilters) => {
    const params = new URLSearchParams();

    // Add filters to query params with enhanced validation
    Object.entries(filters).forEach(([key, value]) => {
      // Skip undefined, null, empty strings, and empty arrays
      if (value === undefined || value === null || value === "") {
        return;
      }

      // Handle array values (like hotel_id, assigned_to, status)
      if (Array.isArray(value)) {
        if (value.length > 0) {
          params.append(key, value.join(","));
        }
        return;
      }

      // Validate date parameters with strict ISO datetime validation
      if (key.includes("date") && typeof value === "string") {
        try {
          const date = new Date(value);
          // Check if it's a valid date and the string is in ISO format
          if (!isNaN(date.getTime()) && value.includes("T")) {
            params.append(key, value);
          } else {
            console.warn(
              `Invalid date format for ${key}:`,
              value,
              "Expected ISO datetime string"
            );
          }
        } catch (error) {
          console.warn(`Invalid date value for ${key}:`, value, error);
        }
      } else if (typeof value === "string" || typeof value === "number") {
        params.append(key, value.toString());
      }
    });

    const url = `/admin/concierge-management/orders${
      params.toString() ? `?${params.toString()}` : ""
    }`;

    try {
      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `HTTP ${response.status}: ${response.statusText} - ${errorText}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to fetch bookings:", error);
      // Return empty result instead of throwing to prevent UI crashes
      return {
        items: [],
        count: 0,
        total_count: 0,
        limit: filters.limit || 20,
        offset: filters.offset || 0,
      };
    }
  };

  // Fetch hotels for the dropdown
  const { data: hotels = [] } = useQuery({
    queryKey: ["hotels", "list-basic"],
    queryFn: fetchHotels,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  // Fetch users for the assigned_to dropdown
  const { data: users = [] } = useQuery({
    queryKey: ["users", "list"],
    queryFn: fetchUsers,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  // Use direct API call to fetch bookings
  const {
    data: bookingData,
    isLoading: bookingsLoading,
    error: bookingsError,
  } = useQuery({
    queryKey: ["concierge-orders", filters, searchParams.toString()],
    queryFn: () => fetchBookings(filters),
    staleTime: 0, // No stale time to ensure fresh data on filter changes
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Ensure query is enabled
  });

  // Extract data from queries
  const bookings = bookingData?.items || [];
  const totalCount = bookingData?.count || 0;
  const isLoading = bookingsLoading;

  // Handle errors
  if (bookingsError) {
    console.error("Error fetching bookings:", bookingsError);
  }

  // Column helper for type safety
  const columnHelper = createColumnHelper<BookingScreenData>();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const handleCopyBookingId = (bookingId: string) => {
    navigator.clipboard.writeText(bookingId);
    toast.success("Booking ID copied to clipboard");
  };

  // Handle booking updates (for kanban drag and drop)
  const handleBookingUpdate = async (
    bookingId: string,
    updates: Partial<BookingScreenData>
  ) => {
    try {
      const response = await fetch(
        `/admin/concierge-management/orders/${bookingId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(updates),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update booking");
      }

      // Invalidate and refetch the bookings query
      queryClient.invalidateQueries({
        queryKey: ["concierge-orders"],
      });

      toast.success("Booking updated successfully");
    } catch (error) {
      console.error("Error updating booking:", error);
      toast.error("Failed to update booking");
      throw error;
    }
  };

  // Handle booking click navigation
  const handleBookingClick = (booking: BookingScreenData) => {
    navigate(`/concierge-management/bookings/${booking.id}`);
  };

  // Handle filter changes
  const handleFiltersChange = (filters: BookingFilterState) => {
    setFilterState(filters);
    // The URL params are already updated by the BookingFilterControls component
    // The filters will be applied through the existing filters useMemo
  };

  // Handle individual filter removal
  const handleRemoveFilter = (
    filterKey: keyof BookingFilterState,
    value?: string
  ) => {
    const newFilters = { ...filterState };

    if (value && Array.isArray(newFilters[filterKey])) {
      // Remove specific value from array
      const arrayFilter = newFilters[filterKey] as string[];
      newFilters[filterKey] = arrayFilter.filter(
        (item) => item !== value
      ) as any;
    } else {
      // Clear the entire filter
      if (
        filterKey === "hotel_id" ||
        filterKey === "assigned_to" ||
        filterKey === "status"
      ) {
        newFilters[filterKey] = [];
      } else if (
        filterKey === "check_in_date_gte" ||
        filterKey === "check_in_date_lte"
      ) {
        newFilters.check_in_date_gte = undefined;
        newFilters.check_in_date_lte = undefined;
      } else if (
        filterKey === "check_out_date_gte" ||
        filterKey === "check_out_date_lte"
      ) {
        newFilters.check_out_date_gte = undefined;
        newFilters.check_out_date_lte = undefined;
      } else {
        newFilters[filterKey] = "" as any;
      }
    }

    handleFiltersChange(newFilters);
  };

  // Handle clear all filters
  const handleClearAllFilters = () => {
    const clearedFilters: BookingFilterState = {
      search: "",
      hotel_id: [],
      assigned_to: [],
      status: [],
    };
    handleFiltersChange(clearedFilters);
  };

  // Define columns
  const columns = useMemo<ColumnDef<BookingScreenData, any>[]>(
    () => [
      columnHelper.display({
        id: "booking_id",
        header: "Booking ID",
        cell: ({ row }) => {
          const booking = row.original;
          return (
            <div className="flex items-center gap-x-3 w-[160px] truncate">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle">
                <Calendar className="h-4 w-4 text-ui-fg-subtle" />
              </div>
              <div>
                <Text className="txt-compact-medium-plus" weight="plus">
                  {booking.order_id}
                </Text>
                <div className="flex items-center gap-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopyBookingId(String(booking.id));
                    }}
                    className="text-ui-fg-muted hover:text-ui-fg-subtle"
                  >
                    <Copy className="h-3 w-3" />
                  </button>
                </div>
              </div>
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "customer_name",
        header: "Customer Name",
        cell: ({ row }) => {
          const booking = row.original;

          // Use the enhanced API response fields
          let customerName = "—";

          if (booking.customer_first_name || booking.customer_last_name) {
            // From enhanced API response
            customerName = `${booking.customer_first_name || ""} ${
              booking.customer_last_name || ""
            }`.trim();
          } else if (booking.customer_email) {
            // Fallback to email if no name available
            customerName = booking.customer_email;
          } else if (
            booking.order?.customer?.first_name ||
            booking.order?.customer?.last_name
          ) {
            // Fallback to order customer data
            customerName = `${booking.order.customer.first_name || ""} ${
              booking.order.customer.last_name || ""
            }`.trim();
          }

          return (
            <Text className="txt-compact-medium w-[140px] truncate">
              {customerName}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "check_in_date",
        header: "Check-in Date",
        cell: ({ row }) => {
          const booking = row.original;

          // Use the enhanced API response field first, then fallback to order metadata
          let checkIn = booking.check_in_date;

          // Fallback to order metadata if not available in concierge order
          if (!checkIn) {
            const orderItems = booking.order?.items || [];
            const firstItemMetadata = orderItems[0]?.metadata || {};
            const orderMetadata = booking.order?.metadata || {};
            checkIn =
              firstItemMetadata.check_in_date || orderMetadata.check_in_date;
          }

          if (!checkIn) return <Text className="txt-compact-medium">—</Text>;

          return (
            <Text className="txt-compact-medium w-[120px]">
              {new Date(checkIn).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "check_out_date",
        header: "Check-out Date",
        cell: ({ row }) => {
          const booking = row.original;

          // Use the enhanced API response field first, then fallback to order metadata
          let checkOut = booking.check_out_date;

          // Fallback to order metadata if not available in concierge order
          if (!checkOut) {
            const orderItems = booking.order?.items || [];
            const firstItemMetadata = orderItems[0]?.metadata || {};
            const orderMetadata = booking.order?.metadata || {};
            checkOut =
              firstItemMetadata.check_out_date || orderMetadata.check_out_date;
          }

          if (!checkOut) return <Text className="txt-compact-medium">—</Text>;

          return (
            <Text className="txt-compact-medium w-[120px]">
              {new Date(checkOut).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "hotel_name",
        header: "Hotel Name",
        cell: ({ row }) => {
          const booking = row.original;

          // Use the enhanced API response field first, then fallback to order metadata
          let hotelName = booking.hotel_name;

          // Fallback to order metadata if not available in enhanced response
          if (!hotelName) {
            const orderItems = booking.order?.items || [];
            const firstItemMetadata = orderItems[0]?.metadata || {};
            const orderMetadata = booking.order?.metadata || {};

            hotelName =
              firstItemMetadata.hotel_name || orderMetadata.hotel_name;
          }

          return (
            <Text className="txt-compact-medium w-[140px] truncate">
              {hotelName || "—"}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "total_price",
        header: "Total Price",
        cell: ({ row }) => {
          const booking = row.original;

          // Use order_total from the API response
          const totalAmount = booking.order_total || booking.order?.total || 0;
          const currency =
            booking.order_currency_code ||
            booking.order?.currency_code ||
            "CHF";

          return (
            <Text className="txt-compact-medium font-medium w-[100px]">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currency,
              }).format(totalAmount / 100)}{" "}
              {/* Assuming amount is in cents */}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "paid",
        header: "Paid",
        cell: ({ row }) => {
          const booking = row.original;

          // Calculate paid amount from payment collections
          let paidAmount = 0;
          if (
            booking.payment_collections &&
            booking.payment_collections.length > 0
          ) {
            paidAmount = booking.payment_collections.reduce((sum, pc) => {
              // Use captured_amount if available, otherwise use amount
              const amount = pc.captured_amount || pc.amount || 0;
              return sum + amount;
            }, 0);
          } else {
            // Fallback to order metadata if payment collections not available
            const orderMetadata = booking.order?.metadata || {};
            const paymentInfo = orderMetadata.payment_info || {};
            paidAmount = paymentInfo.paid_amount || 0;
          }

          const currency =
            booking.order_currency_code ||
            booking.order?.currency_code ||
            "CHF";

          return (
            <Text className="txt-compact-medium w-[100px]">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currency,
              }).format(paidAmount / 100)}{" "}
              {/* Assuming amount is in cents */}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "remaining",
        header: "Remaining",
        cell: ({ row }) => {
          const booking = row.original;

          // Use order_total from the API response
          const totalAmount = booking.order_total || booking.order?.total || 0;

          // Calculate paid amount from payment collections
          let paidAmount = 0;
          if (
            booking.payment_collections &&
            booking.payment_collections.length > 0
          ) {
            paidAmount = booking.payment_collections.reduce((sum, pc) => {
              const amount = pc.captured_amount || pc.amount || 0;
              return sum + amount;
            }, 0);
          } else {
            // Fallback to order metadata
            const orderMetadata = booking.order?.metadata || {};
            const paymentInfo = orderMetadata.payment_info || {};
            paidAmount = paymentInfo.paid_amount || 0;
          }

          const remainingAmount = totalAmount - paidAmount;
          const currency =
            booking.order_currency_code ||
            booking.order?.currency_code ||
            "CHF";

          return (
            <Text className="txt-compact-medium w-[100px]">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currency,
              }).format(remainingAmount / 100)}{" "}
              {/* Assuming amount is in cents */}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "assigned_to",
        header: "Assigned To",
        cell: ({ row }) => {
          const booking = row.original;
          const assignedTo = booking.assigned_to;

          return (
            <Text className="txt-compact-medium w-[120px] truncate">
              {assignedTo || "—"}
            </Text>
          );
        },
      }),
      columnHelper.display({
        id: "status",
        header: "Status",
        cell: ({ row }) => {
          const booking = row.original;
          // Use concierge order status from enhanced API response
          const conciergeStatus = booking.status || "not_started";

          return (
            <Badge color={getStatusBadgeVariant(conciergeStatus)} size="xsmall">
              <span className="inter-small-semibold">
                {conciergeStatus?.replace("_", " ").charAt(0).toUpperCase() +
                  conciergeStatus?.replace("_", " ").slice(1)}
              </span>
            </Badge>
          );
        },
      }),
      columnHelper.display({
        id: "guest",
        header: "Guest",
        cell: ({ row }) => {
          const booking = row.original;

          // Use enhanced API response fields first, then fallback to order metadata
          let guestName = "";
          if (booking.customer_first_name || booking.customer_last_name) {
            guestName = `${booking.customer_first_name || ""} ${
              booking.customer_last_name || ""
            }`.trim();
          }

          let guestEmail = booking.customer_email;

          // Fallback to order metadata if not available in enhanced response
          if (!guestName || !guestEmail) {
            const orderItems = booking.order?.items || [];
            const firstItemMetadata = orderItems[0]?.metadata || {};
            const orderMetadata = booking.order?.metadata || {};

            guestName =
              guestName ||
              firstItemMetadata.guest_name ||
              orderMetadata.guest_name;
            guestEmail =
              guestEmail ||
              firstItemMetadata.guest_email ||
              orderMetadata.guest_email ||
              booking.order?.email;
          }

          return (
            <div className="w-[200px] truncate">
              <Text className="txt-compact-medium">{guestName || "—"}</Text>
              <Text className="txt-compact-small text-ui-fg-subtle ">
                {guestEmail || "—"}
              </Text>
            </div>
          );
        },
      }),
      // columnHelper.display({
      //   id: "actions",
      //   header: "",
      //   cell: ({ row }) => {
      //     const booking = row.original;
      //     return (
      //       <DropdownMenu>
      //         <DropdownMenu.Trigger asChild>
      //           <Button variant="transparent" size="small">
      //             <MoreHorizontal className="h-4 w-4" />
      //           </Button>
      //         </DropdownMenu.Trigger>
      //         <DropdownMenu.Content>
      //           <DropdownMenu.Item
      //             onClick={() => navigate(`/concierge-management/bookings/${booking.order_id || booking.order?.display_id || booking.id}`)}
      //           >
      //             <Eye className="h-4 w-4 mr-2" />
      //             View
      //           </DropdownMenu.Item>
      //           {hasPermission("concierge_management:edit") && (
      //             <DropdownMenu.Item
      //               onClick={() => navigate(`/concierge-management/bookings/${booking.order_id || booking.order?.display_id || booking.id}/edit`)}
      //             >
      //               <Edit className="h-4 w-4 mr-2" />
      //               Edit
      //             </DropdownMenu.Item>
      //           )}
      //           {hasPermission("concierge_management:delete") && (
      //             <DropdownMenu.Item
      //               onClick={() => {
      //                 console.log("Delete booking:", booking.order_id || booking.order?.display_id || booking.id);
      //               }}
      //               className="text-red-600"
      //             >
      //               <Trash className="h-4 w-4 mr-2" />
      //               Delete
      //             </DropdownMenu.Item>
      //           )}
      //         </DropdownMenu.Content>
      //       </DropdownMenu>
      //     );
      //   },
      // }),
    ],
    [hotelId, hasPermission, navigate]
  );

  // Define table filters
  const tableFilters: Filter[] = useMemo(
    () => [
      {
        key: "status",
        label: "Concierge Status",
        type: "select",
        options: [
          { label: "Not Started", value: "not_started" },
          { label: "In Progress", value: "in_progress" },
          { label: "Waiting Customer", value: "waiting_customer" },
          { label: "Ready to Finalize", value: "ready_to_finalize" },
          { label: "Completed", value: "completed" },
        ],
      },
      {
        key: "assigned_to",
        label: "Assigned To",
        type: "select",
        options: [
          { label: "All Assignees", value: "" },
          { label: "Unassigned", value: "unassigned" },
          ...users.map((user: any) => ({
            label: getUserDisplayName(user),
            value: user.id,
          })),
        ],
      },
      {
        key: "hotel_id",
        label: "Hotel",
        type: "select",
        options: [
          { label: "All Hotels", value: "" },
          ...hotels.map((hotel: any) => ({
            label: hotel.name,
            value: hotel.id,
          })),
        ],
      },
      {
        key: "customer_name",
        label: "Customer Name",
        type: "string",
      },
      {
        key: "check_in_date",
        label: "Check-in Date Range",
        type: "date",
      },
      {
        key: "check_out_date",
        label: "Check-out Date Range",
        type: "date",
      },
    ],
    [hotels, users]
  );

  // Define sortable columns
  const orderBy = useMemo(
    () => [
      { key: "order_id" as keyof BookingScreenData, label: "Booking ID" },
      { key: "status" as keyof BookingScreenData, label: "Concierge Status" },
      { key: "assigned_to" as keyof BookingScreenData, label: "Assigned To" },
      {
        key: "check_in_date" as keyof BookingScreenData,
        label: "Check-in Date",
      },
      {
        key: "check_out_date" as keyof BookingScreenData,
        label: "Check-out Date",
      },
      { key: "hotel_name" as keyof BookingScreenData, label: "Hotel Name" },
      {
        key: "customer_first_name" as keyof BookingScreenData,
        label: "Customer Name",
      },
      { key: "order_total" as keyof BookingScreenData, label: "Total Price" },
      { key: "created_at" as keyof BookingScreenData, label: "Created At" },
    ],
    []
  );

  // Custom next/prev handlers for pagination
  const handleNextPage = () => {
    const nextPage = currentPage + 1;
    const params = new URLSearchParams(location.search);
    params.set("page", nextPage.toString());
    navigate(`${location.pathname}?${params.toString()}`);
  };

  const handlePreviousPage = () => {
    const prevPage = Math.max(currentPage - 1, 1);
    const params = new URLSearchParams(location.search);
    params.set("page", prevPage.toString());
    navigate(`${location.pathname}?${params.toString()}`);
  };

  // Create table instance
  const table = useReactTable({
    data: bookings,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    pageCount: Math.ceil(totalCount / pageSize),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
  });

  // Override only nextPage and previousPage methods
  table.nextPage = handleNextPage;
  table.previousPage = handlePreviousPage;

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Concierge Bookings</Heading>
        </div>
      </div>

      {/* DataTable Query Controls - shown for all view modes */}
      <DataTableQuery
        search="autofocus"
        filters={tableFilters}
        orderBy={currentView === "list" ? (orderBy as any) : undefined}
        views={["list", "kanban", "calendar"]}
      />

      {/* Content based on view mode */}
      {currentView === "list" && (
        <DataTableRoot
          table={table}
          columns={columns}
          count={totalCount}
          pagination
          navigateTo={(row) =>
            `/concierge-management/bookings/${row.original.id}`
          }
        />
      )}

      {currentView === "kanban" && (
        <KanbanView
          bookings={bookings}
          onBookingUpdate={handleBookingUpdate}
          onBookingClick={handleBookingClick}
          isLoading={isLoading}
        />
      )}

      {currentView === "calendar" && (
        <CalendarView
          bookings={bookings}
          onBookingClick={handleBookingClick}
          isLoading={isLoading}
        />
      )}

      <Toaster />
    </Container>
  );
};

export default BookingsPageClient;
