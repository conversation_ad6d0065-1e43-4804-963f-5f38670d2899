import {
    Heading,
    Button,
    FocusModal,
    toast,
    Label,
} from "@camped-ai/ui";

import {
    Download,
    ArrowLeft,
} from "lucide-react";

import { useCategories, useCategory } from "../../../../hooks/supplier-products-services/use-categories";
import { useNavigate } from "react-router-dom";
import { defineRouteConfig } from "@camped-ai/admin-sdk";

import { useState, useEffect } from "react";

export const ProductServicesExport = () => {
    const navigate = useNavigate();

    // Export modal state
    const [exportCategoryId, setExportCategoryId] = useState<string>("");
    const [selectedCustomFields, setSelectedCustomFields] = useState<
        Record<string, boolean>
    >({});
    const [isExporting, setIsExporting] = useState(false);
    const [exportFormat, setExportFormat] = useState<"excel" | "csv">("excel");
    const [exportFilters, setExportFilters] = useState({
        status: "all",
    });


    const [selectedExportFields, setSelectedExportFields] = useState<
        Record<string, boolean>
    >({
        name: true,
        category_name: true,
        unit_type_name: true,
        base_cost: true,
        status: true,
        description: true,
    });
    const { data: selectedCategoryData } = useCategory(exportCategoryId);




    // Fetch categories for import template generation
    const { data: importCategoriesData } =
        useCategories({ limit: 100 });

    // Effect to populate custom fields when category changes
    useEffect(() => {
        if (selectedCategoryData?.category?.dynamic_field_schema) {
            console.log('Export: All dynamic fields:', selectedCategoryData.category.dynamic_field_schema);
            const customFields = selectedCategoryData.category.dynamic_field_schema
                .filter((field: any) => {
                    const isSupplierContext = field.field_context === "supplier";
                    const isUsedInProductServices = field.used_in_product_services === true;
                    console.log(`Export Field ${field.key}: field_context=${field.field_context}, used_in_product_services=${field.used_in_product_services}, isSupplierContext=${isSupplierContext}, isUsedInProductServices=${isUsedInProductServices}`);
                    return isSupplierContext && isUsedInProductServices;
                })
                .reduce((acc: Record<string, boolean>, field: any) => {
                    acc[field.key] = true; // Default to selected
                    return acc;
                }, {});
            console.log('Export: Filtered custom fields:', customFields);
            setSelectedCustomFields(customFields);
        } else {
            setSelectedCustomFields({});
        }
    }, [selectedCategoryData]);


    // Export functionality
    const handleExport = async () => {
        if (!exportCategoryId) {
            toast.error("Please select a category first");
            return;
        }

        const selectedCategory = importCategoriesData?.categories.find(
            (cat) => cat.id === exportCategoryId
        );
        if (!selectedCategory) {
            toast.error("Selected category not found");
            return;
        }

        setIsExporting(true);

        try {
            // Build query parameters
            const queryParams = new URLSearchParams();
            queryParams.append("category_id", exportCategoryId);
            queryParams.append("format", exportFormat);

            // Add selected fields to the query parameters
            const selectedFieldsList = Object.entries(selectedExportFields)
                .filter(([_, isSelected]) => isSelected)
                .map(([field, _]) => field);

            // Add selected custom fields to the list
            const selectedCustomFieldsList = Object.entries(selectedCustomFields)
                .filter(([_, isSelected]) => isSelected)
                .map(([field, _]) => field);

            const allSelectedFields = [...selectedFieldsList, ...selectedCustomFieldsList];

            if (allSelectedFields.length > 0) {
                queryParams.append("fields", allSelectedFields.join(","));
            }

            // Add filters
            if (exportFilters.status !== "all") {
                queryParams.append("status", exportFilters.status);
            }

            // Make export request
            const response = await fetch(
                `/admin/supplier-management/products-services/export?${queryParams.toString()}`,
                {
                    method: "GET",
                    credentials: "include",
                }
            );

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(
                    errorData.message || `Export failed with status ${response.status}`
                );
            }

            // Get filename from response headers
            const contentDisposition = response.headers.get("Content-Disposition");
            const filename = contentDisposition
                ? contentDisposition.split("filename=")[1]?.replace(/"/g, "")
                : `products_services_export_${selectedCategory.name}_${new Date().toISOString().split("T")[0]
                }.${exportFormat === "excel" ? "xlsx" : "csv"}`;

            // Create download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            toast.success(
                `Export for ${selectedCategory.name} completed successfully`
            );
            // setShowExportModal(false);
        } catch (error) {
            console.error("Error exporting data:", error);
            const errorMessage =
                error instanceof Error ? error.message : "Unknown error occurred";
            toast.error(`Failed to export data: ${errorMessage}`);
        } finally {
            setIsExporting(false);
        }
    };

    // Handle navigation back to main page
    const handleBackToMain = () => {
        navigate("/supplier-management/products-services");
    };




    return (
        <FocusModal open={true} onOpenChange={handleBackToMain}>
            <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
                <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex justify-between items-center w-full py-4 px-6">
                        <div className="flex items-center gap-3">
                            <Heading
                                level="h2"
                                className="text-xl font-semibold text-gray-900 dark:text-gray-100"
                            >
                                Export Products & Services
                            </Heading>
                        </div>
                    </div>

                    {/* Progress Indicator */}
                    <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                {/* Step 1 */}
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                                        1
                                    </div>
                                    <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">
                                        Configure
                                    </span>
                                </div>
                                <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                                {/* Step 2 */}
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold">
                                        2
                                    </div>
                                    <span className="ml-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                                        Export
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </FocusModal.Header>

                <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
                    <div className="flex-grow overflow-y-auto p-6 pb-0">
                        <div className="flex flex-col gap-6">
                            {/* Category Selection Section - Required */}
                            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                                    <Heading
                                        level="h3"
                                        className="text-lg font-semibold text-gray-900 dark:text-gray-100"
                                    >
                                        Select Category <span className="text-red-500">*</span>
                                    </Heading>
                                </div>
                                <div className="p-4">
                                    <select
                                        value={exportCategoryId}
                                        onChange={(e) => setExportCategoryId(e.target.value)}
                                        className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                        required
                                    >
                                        <option value="">Select a category...</option>
                                        {importCategoriesData?.categories.map((category) => (
                                            <option key={category.id} value={category.id}>
                                                {category.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            {/* Fields Selection Section - Full Width Top Row */}
                            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                                    <div className="flex justify-between items-center">
                                        <Heading
                                            level="h3"
                                            className="text-lg font-semibold text-gray-900 dark:text-gray-100"
                                        >
                                            Select Fields to Export
                                        </Heading>
                                        <Button
                                            variant="secondary"
                                            size="small"
                                            onClick={() => {
                                                const allStandardSelected = Object.values(selectedExportFields).every(Boolean);
                                                const allCustomSelected = Object.values(selectedCustomFields).every(Boolean);
                                                const allSelected = allStandardSelected && allCustomSelected;

                                                const newStandardState = allSelected
                                                    ? Object.keys(selectedExportFields).reduce(
                                                        (acc, key) => ({ ...acc, [key]: false }),
                                                        {}
                                                    )
                                                    : Object.keys(selectedExportFields).reduce(
                                                        (acc, key) => ({ ...acc, [key]: true }),
                                                        {}
                                                    );

                                                const newCustomState = allSelected
                                                    ? Object.keys(selectedCustomFields).reduce(
                                                        (acc, key) => ({ ...acc, [key]: false }),
                                                        {}
                                                    )
                                                    : Object.keys(selectedCustomFields).reduce(
                                                        (acc, key) => ({ ...acc, [key]: true }),
                                                        {}
                                                    );

                                                setSelectedExportFields(newStandardState);
                                                setSelectedCustomFields(newCustomState);
                                            }}
                                            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600"
                                        >
                                            {Object.values(selectedExportFields).every(Boolean) && Object.values(selectedCustomFields).every(Boolean)
                                                ? "Deselect All"
                                                : "Select All"}
                                        </Button>
                                    </div>
                                </div>

                                <div className="p-4 space-y-4">
                                    {/* Standard Fields Section */}
                                    <div>
                                        <div className="mb-3 pb-2 border-b border-gray-200 dark:border-gray-600">
                                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Standard Fields
                                            </h4>
                                        </div>
                                        <div className="grid grid-cols-8 gap-3">
                                            {Object.entries(selectedExportFields).map(
                                                ([field, isSelected]) => {
                                                    const fieldLabels: Record<string, string> = {
                                                        name: "Name",
                                                        category_name: "Category Name",
                                                        unit_type_name: "Unit Type",
                                                        base_cost: "Base Cost",
                                                        status: "Status",
                                                        description: "Description",
                                                    };

                                                    return (
                                                        <div
                                                            key={field}
                                                            className="flex items-center gap-2 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600 h-12 min-w-0"
                                                        >
                                                            <input
                                                                type="checkbox"
                                                                id={`field-${field}`}
                                                                checked={isSelected}
                                                                onChange={(e) =>
                                                                    setSelectedExportFields((prev) => ({
                                                                        ...prev,
                                                                        [field]: e.target.checked,
                                                                    }))
                                                                }
                                                                className="h-4 w-4 flex-shrink-0 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                                                            />
                                                            <Label
                                                                htmlFor={`field-${field}`}
                                                                className="cursor-pointer text-gray-700 dark:text-gray-300 text-xs font-medium truncate min-w-0 flex-1"
                                                            >
                                                                {fieldLabels[field] || field}
                                                            </Label>
                                                        </div>
                                                    );
                                                }
                                            )}
                                        </div>
                                    </div>

                                    {/* Custom Fields Section */}
                                    {Object.keys(selectedCustomFields).length > 0 && (
                                        <div>
                                            <div className="mb-3 pb-2 border-b border-gray-200 dark:border-gray-600">
                                                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    Custom Fields
                                                </h4>
                                            </div>
                                            <div className="grid grid-cols-8 gap-3">
                                                {Object.entries(selectedCustomFields).map(
                                                    ([field, isSelected]) => {
                                                        // Get field label from category schema
                                                        const fieldSchema = selectedCategoryData?.category?.dynamic_field_schema?.find(
                                                            (f: any) => f.key === field
                                                        );
                                                        const fieldLabel = fieldSchema?.label || field;

                                                        return (
                                                            <div
                                                                key={`custom-${field}`}
                                                                className="flex items-center gap-2 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600 h-12 min-w-0"
                                                            >
                                                                <input
                                                                    type="checkbox"
                                                                    id={`custom-field-${field}`}
                                                                    checked={isSelected}
                                                                    onChange={(e) =>
                                                                        setSelectedCustomFields((prev) => ({
                                                                            ...prev,
                                                                            [field]: e.target.checked,
                                                                        }))
                                                                    }
                                                                    className="h-4 w-4 flex-shrink-0 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                                                                />
                                                                <Label
                                                                    htmlFor={`custom-field-${field}`}
                                                                    className="cursor-pointer text-gray-700 dark:text-gray-300 text-xs font-medium truncate min-w-0 flex-1"
                                                                >
                                                                    {fieldLabel}
                                                                </Label>
                                                            </div>
                                                        );
                                                    }
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Bottom Row - Filters, Format, and Summary */}
                            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                                {/* Filters Section */}
                                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                        <Heading
                                            level="h3"
                                            className="text-base font-semibold text-gray-900 dark:text-gray-100"
                                        >
                                            Filters
                                        </Heading>
                                    </div>

                                    <div className="p-4 space-y-4">
                                        <div>
                                            <Label
                                                htmlFor="export_status"
                                                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                                            >
                                                Active Status
                                            </Label>
                                            <select
                                                id="export_status"
                                                value={exportFilters.status}
                                                onChange={(e) =>
                                                    setExportFilters((prev) => ({
                                                        ...prev,
                                                        status: e.target.value,
                                                    }))
                                                }
                                                className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                            >
                                                <option value="all">All Products & Services</option>
                                                <option value="active">Active Only</option>
                                                <option value="inactive">Inactive Only</option>
                                            </select>
                                        </div>


                                    </div>
                                </div>

                                {/* Format Section */}
                                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                        <Heading
                                            level="h3"
                                            className="text-base font-semibold text-gray-900 dark:text-gray-100"
                                        >
                                            Export Format
                                        </Heading>
                                    </div>

                                    <div className="p-4 space-y-3">
                                        <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                                            <input
                                                type="radio"
                                                id="format-xlsx"
                                                name="exportFormat"
                                                value="excel"
                                                checked={exportFormat === "excel"}
                                                onChange={(e) =>
                                                    setExportFormat(e.target.value as "excel" | "csv")
                                                }
                                                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                                            />
                                            <div className="flex-grow">
                                                <Label
                                                    htmlFor="format-xlsx"
                                                    className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium"
                                                >
                                                    Excel (.xlsx)
                                                </Label>
                                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                    Recommended for data analysis
                                                </p>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                                            <input
                                                type="radio"
                                                id="format-csv"
                                                name="exportFormat"
                                                value="csv"
                                                checked={exportFormat === "csv"}
                                                onChange={(e) =>
                                                    setExportFormat(e.target.value as "excel" | "csv")
                                                }
                                                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                                            />
                                            <div className="flex-grow">
                                                <Label
                                                    htmlFor="format-csv"
                                                    className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium"
                                                >
                                                    CSV (.csv)
                                                </Label>
                                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                    Compatible with most applications
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Export Summary Section */}
                                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                        <Heading
                                            level="h3"
                                            className="text-base font-semibold text-gray-900 dark:text-gray-100"
                                        >
                                            Export Summary
                                        </Heading>
                                    </div>

                                    <div className="p-4 space-y-4">
                                        {/* Category Selection Summary */}
                                        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 border border-orange-200 dark:border-orange-800">
                                            <div className="flex items-center justify-between mb-2">
                                                <h4 className="text-sm font-semibold text-orange-900 dark:text-orange-100">
                                                    Selected Category
                                                </h4>
                                                <span
                                                    className={`text-xs font-medium px-2 py-1 rounded-full ${exportCategoryId
                                                        ? "bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200"
                                                        : "bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200"
                                                        }`}
                                                >
                                                    {exportCategoryId ? "Selected" : "Required"}
                                                </span>
                                            </div>
                                            <div className="text-xs text-orange-700 dark:text-orange-300">
                                                {exportCategoryId
                                                    ? importCategoriesData?.categories.find(
                                                        (cat) => cat.id === exportCategoryId
                                                    )?.name || "Unknown"
                                                    : "Please select a category to continue"}
                                            </div>
                                        </div>

                                        {/* Selected Fields Summary */}
                                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                                            <div className="flex items-center justify-between mb-2">
                                                <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                                                    Selected Fields
                                                </h4>
                                                <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
                                                    {
                                                        Object.values(selectedExportFields).filter(Boolean).length +
                                                        Object.values(selectedCustomFields).filter(Boolean).length
                                                    }{" "}
                                                    of {Object.keys(selectedExportFields).length + Object.keys(selectedCustomFields).length}
                                                </span>
                                            </div>
                                            <div className="text-xs text-blue-700 dark:text-blue-300 max-h-16 overflow-y-auto">
                                                {/* Standard fields */}
                                                {Object.entries(selectedExportFields)
                                                    .filter(([_, selected]) => selected)
                                                    .slice(0, 4)
                                                    .map(([field]) => (
                                                        <div key={field} className="truncate">
                                                            •{" "}
                                                            {field
                                                                .replace(/_/g, " ")
                                                                .replace(/\b\w/g, (l) => l.toUpperCase())}
                                                        </div>
                                                    ))}
                                                {/* Custom fields */}
                                                {Object.entries(selectedCustomFields)
                                                    .filter(([_, selected]) => selected)
                                                    .slice(0, Math.max(0, 4 - Object.values(selectedExportFields).filter(Boolean).length))
                                                    .map(([field]) => {
                                                        const fieldSchema = selectedCategoryData?.category?.dynamic_field_schema?.find(
                                                            (f: any) => f.key === field
                                                        );
                                                        const fieldLabel = fieldSchema?.label || field;
                                                        return (
                                                            <div key={field} className="truncate">
                                                                • {fieldLabel}
                                                            </div>
                                                        );
                                                    })}
                                                {(Object.values(selectedExportFields).filter(Boolean).length +
                                                    Object.values(selectedCustomFields).filter(Boolean).length) > 4 && (
                                                        <div className="text-blue-600 dark:text-blue-400 font-medium">
                                                            +
                                                            {(Object.values(selectedExportFields).filter(Boolean).length +
                                                                Object.values(selectedCustomFields).filter(Boolean).length) - 4}{" "}
                                                            more...
                                                        </div>
                                                    )}
                                            </div>
                                        </div>

                                        {/* Filter & Export Details Combined */}
                                        <div className="grid grid-cols-2 gap-3">
                                            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-800">
                                                <h4 className="text-sm font-semibold text-green-900 dark:text-green-100 mb-2">
                                                    Filters
                                                </h4>
                                                <div className="space-y-1 text-xs text-green-700 dark:text-green-300">
                                                    <div>
                                                        <span className="font-medium">Status: </span>
                                                        {exportFilters.status === "all"
                                                            ? "All"
                                                            : exportFilters.status}
                                                    </div>

                                                </div>
                                            </div>

                                            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800">
                                                <h4 className="text-sm font-semibold text-purple-900 dark:text-purple-100 mb-2">
                                                    Export Details
                                                </h4>
                                                <div className="space-y-1 text-xs text-purple-700 dark:text-purple-300">
                                                    <div>
                                                        <span className="font-medium">Format: </span>
                                                        <span className="uppercase">
                                                            {exportFormat === "excel" ? "xlsx" : "csv"}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Date: </span>
                                                        {new Date().toLocaleDateString()}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Instructions */}
                                        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                                            <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                                                {exportCategoryId
                                                    ? 'Click "Export Data" to download the file to your downloads folder'
                                                    : "Select a category above to enable export"}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/* Footer */}
                    <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                                {exportCategoryId
                                    ? `Ready to export ${Object.values(selectedExportFields).filter(Boolean).length +
                                    Object.values(selectedCustomFields).filter(Boolean).length
                                    } fields`
                                    : "Select a category to continue"}
                            </div>
                            <div className="flex gap-4">
                                <Button
                                    variant="secondary"
                                    onClick={handleBackToMain}
                                    disabled={isExporting}
                                    className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="primary"
                                    onClick={handleExport}
                                    disabled={
                                        !exportCategoryId ||
                                        isExporting ||
                                        (Object.values(selectedExportFields).filter(Boolean).length +
                                            Object.values(selectedCustomFields).filter(Boolean).length) === 0
                                    }
                                    className="flex items-center gap-3 px-6 py-3 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <Download className="w-5 h-5" />
                                    {isExporting ? "Exporting..." : "Export Data"}
                                </Button>
                            </div>
                        </div>
                    </div>
                </FocusModal.Body>
            </FocusModal.Content>
        </FocusModal>
    )
}

export const config = defineRouteConfig({
    label: "Export Products & Services",
    icon: Download,
});

export default ProductServicesExport;