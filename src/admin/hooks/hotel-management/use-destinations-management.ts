import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { useMemo, useCallback, useEffect } from "react";

// Types
export interface DestinationWithImages {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  country: string;
  location?: string;
  tags?: string[] | string;
  is_featured: boolean;
  ai_content?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  images?: Array<{
    id?: string;
    url: string;
    isThumbnail?: boolean;
    metadata?: Record<string, any>;
  }>;
  thumbnailUrl?: string;
  hotelCount?: number;
}

export interface DestinationsManagementResponse {
  destinations: DestinationWithImages[];
  count: number;
  limit: number;
  offset: number;
}

export interface DestinationManagementFilters {
  limit?: number;
  offset?: number;
  page?: number;
  is_featured?: boolean | null;
  is_active?: boolean | null;
  search?: string;
  status?: string | null; // New unified status filter
}

// Query Keys
export const destinationManagementKeys = {
  all: ["destinations-management"] as const,
  lists: () => [...destinationManagementKeys.all, "list"] as const,
  list: (filters: DestinationManagementFilters) =>
    [...destinationManagementKeys.lists(), filters] as const,
};

// Hook for destinations management with URL synchronization
export const useDestinationsManagement = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // Set default URL parameters if none exist
  useEffect(() => {
    const hasParams = searchParams.has("page") || searchParams.has("limit");
    if (!hasParams) {
      const defaultParams = new URLSearchParams();
      defaultParams.set("page", "1");
      defaultParams.set("limit", "10");
      setSearchParams(defaultParams, { replace: true });
    }
  }, [searchParams, setSearchParams]);

  // Parse URL parameters
  const urlFilters = useMemo(() => {
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const offset = (page - 1) * limit;
    const search = searchParams.get("search") || "";
    const is_featured = searchParams.get("is_featured");
    const is_active = searchParams.get("is_active");
    const status = searchParams.get("status");

    return {
      limit,
      offset,
      page,
      search: search || undefined,
      is_featured:
        is_featured === "true" ? true : is_featured === "false" ? false : null,
      is_active:
        is_active === "true" ? true : is_active === "false" ? false : null,
      status: status || null,
    };
  }, [searchParams]);

  // Update URL parameters
  const updateFilters = useCallback(
    (newFilters: Partial<DestinationManagementFilters>) => {
      const newSearchParams = new URLSearchParams(searchParams);

      // Handle pagination
      if (newFilters.limit !== undefined) {
        newSearchParams.set("limit", newFilters.limit.toString());
      }
      if (newFilters.page !== undefined) {
        newSearchParams.set("page", newFilters.page.toString());
      }

      // Handle search
      if (newFilters.search !== undefined) {
        if (newFilters.search.trim()) {
          newSearchParams.set("search", newFilters.search);
        } else {
          newSearchParams.delete("search");
        }
        // Reset to page 1 when search changes
        newSearchParams.set("page", "1");
      }

      // Handle filters
      if (newFilters.is_featured !== undefined) {
        if (newFilters.is_featured !== null) {
          newSearchParams.set("is_featured", newFilters.is_featured.toString());
        } else {
          newSearchParams.delete("is_featured");
        }
        // Reset to page 1 when filters change
        newSearchParams.set("page", "1");
      }

      if (newFilters.is_active !== undefined) {
        if (newFilters.is_active !== null) {
          newSearchParams.set("is_active", newFilters.is_active.toString());
        } else {
          newSearchParams.delete("is_active");
        }
        // Reset to page 1 when filters change
        newSearchParams.set("page", "1");
      }

      // Handle unified status filter
      if (newFilters.status !== undefined) {
        if (newFilters.status && newFilters.status !== "all") {
          newSearchParams.set("status", newFilters.status);
          // Clear individual filters when using unified status
          newSearchParams.delete("is_featured");
          newSearchParams.delete("is_active");
        } else {
          newSearchParams.delete("status");
        }
        // Reset to page 1 when filters change
        newSearchParams.set("page", "1");
      }

      setSearchParams(newSearchParams);
    },
    [searchParams, setSearchParams]
  );

  // Fetch destinations data
  const { data, isLoading, error, refetch, isFetching } = useQuery({
    queryKey: destinationManagementKeys.list(urlFilters),
    queryFn: async (): Promise<DestinationsManagementResponse> => {
      const params = new URLSearchParams();

      if (urlFilters.limit) params.append("limit", urlFilters.limit.toString());
      if (urlFilters.offset)
        params.append("offset", urlFilters.offset.toString());

      // Handle unified status filter or individual filters
      if (urlFilters.status) {
        switch (urlFilters.status) {
          case "featured":
            params.append("is_featured", "true");
            break;
          case "not-featured":
            params.append("is_featured", "false");
            break;
          case "active":
            params.append("is_active", "true");
            break;
          case "inactive":
            params.append("is_active", "false");
            break;
        }
      } else {
        // Use individual filters if no unified status filter
        if (
          urlFilters.is_featured !== null &&
          urlFilters.is_featured !== undefined
        ) {
          params.append("is_featured", urlFilters.is_featured.toString());
        }
        if (
          urlFilters.is_active !== null &&
          urlFilters.is_active !== undefined
        ) {
          params.append("is_active", urlFilters.is_active.toString());
        }
      }

      if (urlFilters.search) params.append("search", urlFilters.search);

      const url = `/admin/hotel-management/destinations${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch destinations");
      }

      const result = await response.json();

      // Fetch hotel counts
      const hotelCountsResponse = await fetch(
        `/admin/hotel-management/destinations/hotel-counts`,
        {
          credentials: "include",
        }
      );

      let hotelCounts: Record<string, number> = {};
      if (hotelCountsResponse.ok) {
        const { counts } = await hotelCountsResponse.json();
        hotelCounts = counts || {};
      }

      // Fetch images for each destination
      const destinationsWithImages = await Promise.all(
        result.destinations.map(async (destination: any) => {
          try {
            const imagesResponse = await fetch(
              `/admin/hotel-management/destinations/${destination.id}/images`,
              { credentials: "include" }
            );

            if (imagesResponse.ok) {
              const imagesData = await imagesResponse.json();

              if (imagesData.images && imagesData.images.length > 0) {
                const thumbnailImage =
                  imagesData.images.find(
                    (img: any) => img.metadata?.isThumbnail
                  ) || imagesData.images[0];

                return {
                  ...destination,
                  images: imagesData.images,
                  thumbnailUrl: thumbnailImage.url,
                  hotelCount: hotelCounts[destination.id] || 0,
                };
              }
            }

            return {
              ...destination,
              hotelCount: hotelCounts[destination.id] || 0,
            };
          } catch (error) {
            console.error(
              `Error fetching images for destination ${destination.id}:`,
              error
            );
            return {
              ...destination,
              hotelCount: hotelCounts[destination.id] || 0,
            };
          }
        })
      );

      return {
        destinations: destinationsWithImages,
        count: result.count,
        limit: result.limit,
        offset: result.offset,
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnWindowFocus: false,
  });

  // Pagination calculations
  const pagination = useMemo(() => {
    const totalCount = data?.count || 0;
    const currentPage = urlFilters.page;
    const pageSize = urlFilters.limit;
    const totalPages = Math.ceil(totalCount / pageSize);
    const canPreviousPage = currentPage > 1;
    const canNextPage = currentPage < totalPages;

    return {
      currentPage,
      pageSize,
      totalPages,
      totalCount,
      canPreviousPage,
      canNextPage,
    };
  }, [data?.count, urlFilters.page, urlFilters.limit]);

  // Pagination functions
  const nextPage = useCallback(() => {
    if (pagination.canNextPage) {
      updateFilters({ page: pagination.currentPage + 1 });
    }
  }, [pagination.canNextPage, pagination.currentPage, updateFilters]);

  const previousPage = useCallback(() => {
    if (pagination.canPreviousPage) {
      updateFilters({ page: pagination.currentPage - 1 });
    }
  }, [pagination.canPreviousPage, pagination.currentPage, updateFilters]);

  const goToPage = useCallback(
    (page: number) => {
      updateFilters({ page });
    },
    [updateFilters]
  );

  const changePageSize = useCallback(
    (newPageSize: number) => {
      updateFilters({ limit: newPageSize, page: 1 });
    },
    [updateFilters]
  );

  // Filter functions
  const updateSearch = useCallback(
    (search: string) => {
      updateFilters({ search });
    },
    [updateFilters]
  );

  const updateFeaturedFilter = useCallback(
    (is_featured: boolean | null) => {
      updateFilters({ is_featured });
    },
    [updateFilters]
  );

  const updateActiveFilter = useCallback(
    (is_active: boolean | null) => {
      updateFilters({ is_active });
    },
    [updateFilters]
  );

  const updateStatusFilter = useCallback(
    (status: string | null) => {
      updateFilters({ status });
    },
    [updateFilters]
  );

  const clearFilters = useCallback(() => {
    updateFilters({
      is_featured: null,
      is_active: null,
      search: "",
      status: null,
      page: 1,
    });
  }, [updateFilters]);

  return {
    // Data
    destinations: data?.destinations || [],
    isLoading,
    isFetching,
    error,
    refetch,

    // Current filters
    filters: urlFilters,

    // Pagination
    pagination,
    nextPage,
    previousPage,
    goToPage,
    changePageSize,

    // Filter functions
    updateSearch,
    updateFeaturedFilter,
    updateActiveFilter,
    updateStatusFilter,
    clearFilters,
  };
};
