import {
  Button,
  Input,
  FocusModal,
  Text,
  Label,
  Switch,
  Heading,
  Textarea,
  Tabs,
  Select,
  Tooltip,
  IconButton,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import DestinationMediaSection from "./destination/destination-media-section";
import { MediaField } from "./hotel/media-item";
import { useState } from "react";
import { Info, Globe, MapPin, DollarSign, Tag, Building } from "lucide-react";
import { CountrySelector } from "./common/country-selector";

export type DestinationFaqData = {
  id?: string;
  question: string;
  answer: string;
};

export type DestinationFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  location: string | null;
  tags: string[] | null;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  faqs?: DestinationFaqData[];
  id?: string;
};

type DestinationFormProps = {
  formData: DestinationFormData;
  setFormData: (data: DestinationFormData) => void;
  onSubmit: () => Promise<boolean>;
  isEdit?: boolean;
  closeModal: () => void;
};

const DestinationForm = ({
  formData,
  setFormData,
  onSubmit,
  isEdit,
  closeModal,
}: DestinationFormProps) => {
  const form = useForm<DestinationFormData>({
    defaultValues: formData,
  });

  const [activeTab, setActiveTab] = useState("basics");
  const [tagsInput, setTagsInput] = useState(
    Array.isArray(formData.tags) ? formData.tags.join(", ") : ""
  );

  // Handle tags input change
  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagsInput(e.target.value);
    // Convert comma-separated string to array
    const tagsArray = e.target.value
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag);
    setFormData({ ...formData, tags: tagsArray });
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Get the form values including media
    const formValues = form.getValues();

    // Ensure tags is properly formatted
    const tagsArray = tagsInput
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag);

    // Update the formData with the form values, preserving FAQ data
    setFormData({
      ...formData,
      ...formValues,
      tags: tagsArray,
      is_featured: formValues.is_featured || false,
      faqs: formData.faqs || [], // Preserve FAQ data
    });

    const success = await onSubmit();
    if (success) {
      closeModal();
    }
  };

  return (
    <FocusModal.Content>
      <FocusModal.Header>
        <div className="flex justify-between items-center">
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={!formData.name || !formData.handle}
          >
            {isEdit ? "Update" : "Save"}
          </Button>
        </div>
      </FocusModal.Header>
      <FocusModal.Body className="flex flex-col items-center py-16 gap-4 overflow-y-auto">
        <Heading level="h1">
          {isEdit ? "Edit Destination" : "Create Destination"}
        </Heading>
        <Tabs defaultValue="general" className="w-full max-w-lg">
          <Tabs.List>
            <Tabs.Trigger value="general">General</Tabs.Trigger>
            <Tabs.Trigger value="media">Media</Tabs.Trigger>
            <Tabs.Trigger value="faqs">FAQs</Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content
            value="general"
            className="flex w-full flex-col gap-y-6 pt-5"
          >
            <div>
              <Text className="mb-2 flex items-center gap-1">
                Destination Name{" "}
                <span className="text-red-500 text-[0.6em]">★</span>
              </Text>
              <Input
                value={formData.name}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    name: e.target.value,
                    handle: e.target.value.toLowerCase().replace(/\\s+/g, "-"),
                  })
                }
                placeholder="Enter destination name"
              />
            </div>
            <div>
              <Text className="mb-2">Description</Text>
              <Textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Enter description"
              />
            </div>
            <div className="flex gap-x-8">
              <div className="flex items-center gap-x-2">
                <Switch
                  id="is-active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) =>
                    setFormData({ ...formData, is_active: checked })
                  }
                />
                <Label htmlFor="is-active">Active</Label>
              </div>
              <div className="flex items-center gap-x-2">
                <Switch
                  id="is-featured"
                  checked={formData.is_featured}
                  onCheckedChange={(checked) =>
                    setFormData({ ...formData, is_featured: checked })
                  }
                />
                <Label htmlFor="is-featured">Featured</Label>
              </div>
            </div>
            <div>
              <Text className="mb-2 flex items-center gap-1">
                Handle <span className="text-red-500 text-[0.6em]">★</span>
              </Text>
              <Input
                value={formData.handle}
                onChange={(e) =>
                  setFormData({ ...formData, handle: e.target.value })
                }
                placeholder="Enter handle"
              />
            </div>
            <div>
              <Text className="mb-2 flex items-center gap-1">
                Country <span className="text-red-500 text-[0.6em]">★</span>
              </Text>
              <CountrySelector
                value={formData.country}
                onChange={(value) =>
                  setFormData({ ...formData, country: value || "" })
                }
                placeholder="Search and select a country"
                allowClear={true}
                className="w-full"
              />
            </div>

            <div>
              <Text className="mb-2">Location</Text>
              <Input
                value={formData.location || ""}
                onChange={(e) =>
                  setFormData({ ...formData, location: e.target.value })
                }
                placeholder="Enter location"
              />
            </div>

          </Tabs.Content>
          <Tabs.Content value="media" className="pt-5">
            <DestinationMediaSection form={form} destinationId={formData.id} />
          </Tabs.Content>
          <Tabs.Content value="faqs" className="pt-5">
            <div className="flex flex-col gap-y-4">
              <div className="flex justify-between items-center">
                <Text className="font-medium">Frequently Asked Questions</Text>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    const newFaq = { question: "", answer: "" };
                    setFormData({
                      ...formData,
                      faqs: [...(formData.faqs || []), newFaq],
                    });
                  }}
                >
                  Add FAQ
                </Button>
              </div>
              {formData.faqs && formData.faqs.length > 0 ? (
                <div className="space-y-4">
                  {formData.faqs.map((faq, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 space-y-3"
                    >
                      <div className="flex justify-between items-start">
                        <Text className="text-sm font-medium">
                          FAQ {index + 1}
                        </Text>
                        <IconButton
                          variant="transparent"
                          size="small"
                          onClick={() => {
                            const updatedFaqs =
                              formData.faqs?.filter((_, i) => i !== index) ||
                              [];
                            setFormData({ ...formData, faqs: updatedFaqs });
                          }}
                        >
                          ×
                        </IconButton>
                      </div>
                      <div>
                        <Label className="text-xs text-gray-600">
                          Question
                        </Label>
                        <Input
                          value={faq.question}
                          onChange={(e) => {
                            const updatedFaqs = [...(formData.faqs || [])];
                            updatedFaqs[index] = {
                              ...faq,
                              question: e.target.value,
                            };
                            setFormData({ ...formData, faqs: updatedFaqs });
                          }}
                          placeholder="Enter question"
                        />
                      </div>
                      <div>
                        <Label className="text-xs text-gray-600">Answer</Label>
                        <Textarea
                          value={faq.answer}
                          onChange={(e) => {
                            const updatedFaqs = [...(formData.faqs || [])];
                            updatedFaqs[index] = {
                              ...faq,
                              answer: e.target.value,
                            };
                            setFormData({ ...formData, faqs: updatedFaqs });
                          }}
                          placeholder="Enter answer"
                          rows={3}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Text>
                    No FAQs added yet. Click "Add FAQ" to get started.
                  </Text>
                </div>
              )}
            </div>
          </Tabs.Content>
        </Tabs>
      </FocusModal.Body>
    </FocusModal.Content>
  );
};

export default DestinationForm;
