import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import DestinationModuleService from "src/modules/hotel-management/destination/service";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type CreateDestinationStepInput = {
  name: string;
  handle: string;
  description?: string;
  is_active?: boolean;
  country: string;
  location?: string;
  tags?: string[] | string;
  is_featured?: boolean;
  ai_content?: string;
  metadata?: Record<string, any>;
  faqs?: Array<{
    question: string;
    answer: string;
  }>;
};

type CreateDestinationWorkflowInput = CreateDestinationStepInput;

export const createDestinationStep = createStep(
  "create-destination-step",
  async (input: CreateDestinationStepInput, { container }) => {
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);

    const destination = await destinationModuleService.createDestinations({
      name: input.name,
      handle: input.handle,
      description: input.description,
      is_active: input.is_active || true,
      country: input.country,
      location: input.location,
      is_featured: input.is_featured || false,
      ai_content: input.ai_content,
      metadata: input.metadata,
      tags: input.tags
        ? ((Array.isArray(input.tags) ? input.tags : [input.tags]) as any)
        : null,
    });

    // Create FAQs if provided
    if (input.faqs && input.faqs.length > 0) {
      console.log(
        `Creating ${input.faqs.length} FAQs for new destination ${destination.id}`
      );
      for (const faq of input.faqs) {
        if (faq.question && faq.answer) {
          try {
            console.log(`Creating FAQ: ${faq.question}`);
            const createdFaq =
              await destinationModuleService.createDestinationFaq({
                question: faq.question,
                answer: faq.answer,
                destination_id: destination.id,
              });
            console.log(`Created FAQ ${createdFaq.id}`);
          } catch (faqError) {
            console.error(`Error creating FAQ:`, faqError);
          }
        }
      }
    }

    return new StepResponse(destination, [destination.id]);
  },
  async (ids: string[], { container }) => {
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);
    await destinationModuleService.deleteDestinations(ids);
  }
);

export const CreateDestinationWorkflow = createWorkflow(
  "create-destination",
  (input: CreateDestinationWorkflowInput) => {
    // First, create the destination
    const destination = createDestinationStep(input);

    // Now we can emit the event with the input data
    emitEventStep({
      eventName: "destination.created",
      data: {
        // We don't have the ID yet since it's a new destination
        destination: destination,
        name: input.name,
        country: input.country,
        is_active: input.is_active || true,
      },
    });

    return new WorkflowResponse(destination);
  }
);
