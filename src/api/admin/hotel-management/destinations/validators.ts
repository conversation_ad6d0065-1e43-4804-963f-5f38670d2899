import { z } from "zod";

const FaqSchema = z.object({
  question: z.string().min(1, "Question is required"),
  answer: z.string().min(1, "Answer is required"),
});

export const PostAdminCreateDestination = z.object({
  name: z.string(),
  handle: z.string(),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  country: z.string(),
  location: z.string().optional(),
  tags: z.array(z.string()).optional(),
  is_featured: z.boolean().default(false),
  ai_content: z.string().optional(),
  faqs: z.array(FaqSchema).optional(),
});

const UpdateFaqSchema = z.object({
  id: z.string().optional(),
  question: z.string().min(1, "Question is required"),
  answer: z.string().min(1, "Answer is required"),
});

export const PostAdminUpdateDestination = z.object({
  id: z.string(),
  name: z.string().optional(),
  handle: z.string().optional(),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  country: z.string().optional(),
  location: z.string().optional(),
  tags: z.array(z.string()).optional(),
  is_featured: z.boolean().default(false),
  ai_content: z.string().optional(),
  faqs: z.array(UpdateFaqSchema).optional(),
});

export const PostAdminDeleteDestination = z.object({
  ids: z.string(),
});
