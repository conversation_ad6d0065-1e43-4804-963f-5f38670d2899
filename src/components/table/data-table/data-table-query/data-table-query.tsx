import { Filter } from "..";
import { DataTableFilter } from "../data-table-filter";
import { DataTableOrderBy, DataTableOrderByKey } from "../data-table-order-by";
import { DataTableSearch } from "../data-table-search";
import { But<PERSON> } from "@camped-ai/ui";
import { Table, Columns, Calendar } from "lucide-react";
import { useSearchParams } from "react-router-dom";


export type ViewMode = "list" | "kanban" | "calendar";

export interface DataTableQueryProps<TData> {
  search?: boolean | "autofocus";
  orderBy?: DataTableOrderByKey<TData>[];
  filters?: Filter[];
  prefix?: string;
  views?: ViewMode[];
}

export const DataTableQuery = <TData,>({
  search,
  orderBy,
  filters,
  prefix,
  views,
}: DataTableQueryProps<TData>) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const currentView = (searchParams.get("view") as ViewMode) || "list";

  const handleViewChange = (view: ViewMode) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view", view);
    setSearchParams(newSearchParams);
  };

  return (
    (search || orderBy || filters || prefix || views) && (
      <div className="flex items-start justify-between gap-x-4 px-6 py-4">
        <div className="w-full max-w-[60%]">
          {filters && filters.length > 0 && (
            <DataTableFilter filters={filters} prefix={prefix} />
          )}
        </div>
        <div className="flex shrink-0 items-center gap-x-2">
          {views && views.length > 1 && (
            <ViewToggle
              viewMode={currentView}
              onViewModeChange={handleViewChange}
              availableViews={views}
            />
          )}
          {search && (
            <DataTableSearch
              prefix={prefix}
              autofocus={search === "autofocus"}
            />
          )}
          {orderBy && <DataTableOrderBy keys={orderBy} prefix={prefix} />}
        </div>
      </div>
    )
  );
};

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  availableViews: ViewMode[];
  className?: string;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  availableViews,
  className = "",
}) => {
  const getViewIcon = (view: ViewMode) => {
    switch (view) {
      case "list":
        return <Table className="w-4 h-4" />;
      case "kanban":
        return <Columns className="w-4 h-4" />;
      case "calendar":
        return <Calendar className="w-4 h-4" />;
      default:
        return <Table className="w-4 h-4" />;
    }
  };

  const getViewTitle = (view: ViewMode) => {
    switch (view) {
      case "list":
        return "Table View";
      case "kanban":
        return "Kanban View";
      case "calendar":
        return "Calendar View";
      default:
        return "Table View";
    }
  };

  return (
    <div className={`flex border border-ui-border-base rounded-md overflow-hidden shadow-sm ${className}`}>
      {availableViews.map((view) => (
        <Button
          key={view}
          variant="transparent"
          size="small"
          onClick={() => onViewModeChange(view)}
          className={`rounded-none border-0 ${
            viewMode === view
              ? "bg-ui-bg-interactive text-white"
              : "hover:bg-ui-bg-base-hover"
          }`}
          title={getViewTitle(view)}
        >
          {getViewIcon(view)}
        </Button>
      ))}
    </div>
  );
};
