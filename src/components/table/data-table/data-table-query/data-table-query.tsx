import { Filter } from "..";
import { DataTableFilter } from "../data-table-filter";
import { DataTableOrderBy, DataTableOrderByKey } from "../data-table-order-by";
import { DataTableSearch } from "../data-table-search";
import { Button } from "@camped-ai/ui";
import { Table, Columns } from "lucide-react";


export interface DataTableQueryProps<TData> {
  search?: boolean | "autofocus";
  orderBy?: DataTableOrderByKey<TData>[];
  filters?: Filter[];
  prefix?: string;
}

export const DataTableQuery = <TData,>({
  search,
  orderBy,
  filters,
  prefix,
}: DataTableQueryProps<TData>) => {
  return (
    (search || orderBy || filters || prefix) && (
      <div className="flex items-start justify-between gap-x-4 px-6 py-4">
        <div className="w-full max-w-[60%]">
          {filters && filters.length > 0 && (
            <DataTableFilter filters={filters} prefix={prefix} />
          )}
        </div>
        <div className="flex shrink-0 items-center gap-x-2">
          <ViewToggle viewMode={viewMode} onViewModeChange={setViewMode} />
          {search && (
            <DataTableSearch
              prefix={prefix}
              autofocus={search === "autofocus"}
            />
          )}
          {orderBy && <DataTableOrderBy keys={orderBy} prefix={prefix} />}
        </div>
      </div>
    )
  );
};

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  className = "",
}) => {
  return (
    <div className={`flex border border-ui-border-base rounded-md overflow-hidden shadow-sm ${className}`}>
      <Button
        variant="transparent"
        size="small"
        onClick={() => onViewModeChange("list")}
        className={`rounded-none border-0 ${
          viewMode === "list"
            ? "bg-ui-bg-interactive text-white"
            : "hover:bg-ui-bg-base-hover"
        }`}
        title="Table View"
      >
        <Table className="w-4 h-4" />
      </Button>
      <Button
        variant="transparent"
        size="small"
        onClick={() => onViewModeChange("kanban")}
        className={`rounded-none border-0 ${
          viewMode === "kanban"
            ? "bg-ui-bg-interactive text-white"
            : "hover:bg-ui-bg-base-hover"
        }`}
        title="Kanban View"
      >
        <Columns className="w-4 h-4" />
      </Button>
    </div>
  );
};
